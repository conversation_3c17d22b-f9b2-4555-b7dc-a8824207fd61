{"name": "react-hotkeys-hook", "description": "React hook for handling keyboard shortcuts", "version": "4.6.2", "repository": {"type": "git", "url": "https://github.com/JohannesKlauss/react-keymap-hook.git"}, "homepage": "https://johannesklauss.github.io/react-hotkeys-hook/", "author": "<PERSON>", "main": "dist/index.js", "module": "dist/react-hotkeys-hook.esm.js", "types": "dist/index.d.ts", "files": ["dist", "src"], "keywords": ["react", "hook", "hooks", "component", "hotkey", "shortcut", "keyboard", "shortcuts", "keypress", "hotkeys"], "license": "MIT", "scripts": {"build": "tsdx build", "test": "jest", "publish": "np", "format": "prettier ./src/**/*.{ts,tsx} --write", "lint": "eslint ./src/**/*.{ts,tsx} --fix && prettier ./src/**/*.{ts,tsx} --check"}, "babel": {"presets": ["@babel/preset-env", "@babel/preset-typescript", "@babel/preset-react"]}, "prettier": {"printWidth": 120, "semi": false, "singleQuote": true, "trailingComma": "es5"}, "eslintConfig": {"env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["react", "@typescript-eslint"], "rules": {"react/react-in-jsx-scope": "off", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-ts-comment": "off"}}, "devDependencies": {"@babel/core": "7.25.2", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-transform-react-jsx": "7.25.2", "@babel/preset-env": "7.25.4", "@babel/preset-react": "7.24.7", "@babel/preset-typescript": "7.24.7", "@testing-library/jest-dom": "6.5.0", "@testing-library/react": "16.0.1", "@testing-library/user-event": "14.5.2", "@types/jest": "29.5.12", "@types/react": "18.3.5", "@types/react-dom": "18.3.0", "@typescript-eslint/eslint-plugin": "8.3.0", "@typescript-eslint/parser": "8.3.0", "eslint": "8.57.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-react": "7.35.0", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "prettier": "2.8.8", "react": "18.3.1", "react-dom": "18.3.1", "react-test-renderer": "18.3.1", "tsdx": "0.14.1", "tslib": "2.7.0", "typescript": "5.5.4"}, "peerDependencies": {"react": ">=16.8.1", "react-dom": ">=16.8.1"}}