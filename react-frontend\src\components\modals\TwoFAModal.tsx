import React, { useState, useEffect } from 'react';
import { authAPI } from '@/utils/api';

interface TwoFAModalProps {
  onClose: () => void;
  onVerify: (code: string) => Promise<{ success: boolean; error?: string }>;
  email?: string;
}

const TwoFAModal: React.FC<TwoFAModalProps> = ({ onClose, onVerify, email }) => {
  const [showQR, setShowQR] = useState(false); // Start with code input for login flow
  const [code, setCode] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [error, setError] = useState('');
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [isLoadingQR, setIsLoadingQR] = useState(false);

  useEffect(() => {
    // Load QR code if email is provided (for setup flow)
    if (email && showQR) {
      loadQRCode();
    }
  }, [email, showQR]);

  const loadQRCode = async () => {
    if (!email) return;

    setIsLoadingQR(true);
    try {
      const result = await authAPI.get2FASetup(email);
      if (result.success && result.qr_url) {
        setQrCodeUrl(result.qr_url);
      } else {
        setError('Failed to load QR code');
      }
    } catch (err) {
      setError('Failed to load QR code');
    } finally {
      setIsLoadingQR(false);
    }
  };

  const handleShowQR = () => {
    setShowQR(true);
    if (email) {
      loadQRCode();
    }
  };

  const handleNextToCodeInput = () => {
    setShowQR(false);
  };

  const handleVerifyCode = async () => {
    if (!code.trim()) {
      setError('Please enter the 2FA code');
      return;
    }

    setIsVerifying(true);
    setError('');

    try {
      const result = await onVerify(code);
      if (!result.success) {
        setError(result.error || 'Invalid 2FA code. Please try again.');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setIsVerifying(false);
    }
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4" onClick={handleOverlayClick}>
      <div className="bg-white rounded-2xl shadow-xl w-full max-w-md mx-auto relative animate-in fade-in-0 zoom-in-95 duration-200">
        {/* Header */}
        <div className="flex items-center justify-between p-6 pb-4">
          <h2 className="text-xl font-semibold text-gray-900">
            Two-Factor Authentication
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors p-1"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Body */}
        <div className="px-6 pb-6">
          {showQR ? (
            /* QR Code Section */
            <div className="text-center space-y-4">
              <p className="text-sm text-gray-600">
                Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.):
              </p>
              <div className="flex justify-center">
                <div className="w-48 h-48 bg-gray-50 border border-gray-200 rounded-lg flex items-center justify-center">
                  {isLoadingQR ? (
                    <div className="flex flex-col items-center space-y-2">
                      <svg className="animate-spin h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span className="text-sm text-gray-500">Loading...</span>
                    </div>
                  ) : qrCodeUrl ? (
                    <img
                      src={qrCodeUrl}
                      alt="2FA QR Code"
                      className="w-40 h-40 object-contain"
                    />
                  ) : (
                    <div className="text-center">
                      <svg className="w-12 h-12 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                      <p className="text-sm text-gray-500">QR code not available</p>
                    </div>
                  )}
                </div>
              </div>
              <button
                onClick={handleNextToCodeInput}
                className="w-full bg-blue-600 text-white font-medium py-2.5 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
              >
                I've scanned the code
              </button>
            </div>
          ) : (
            /* Code Input Section */
            <div className="space-y-4">
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-4">
                  Enter the 6-digit code from your authenticator app
                </p>
              </div>

              <div className="space-y-2">
                <label htmlFor="twoFACode" className="text-sm font-medium text-gray-700">
                  Authentication Code
                </label>
                <input
                  type="text"
                  id="twoFACode"
                  value={code}
                  onChange={(e) => {
                    const value = e.target.value.replace(/\D/g, '').slice(0, 6);
                    setCode(value);
                    setError('');
                  }}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors outline-none text-center text-lg tracking-widest font-mono"
                  placeholder="000000"
                  maxLength={6}
                  autoComplete="one-time-code"
                  disabled={isVerifying}
                />
              </div>

              {/* Error Message */}
              {error && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-sm text-red-600">
                  {error}
                </div>
              )}

              <div className="space-y-3">
                <button
                  onClick={handleVerifyCode}
                  disabled={isVerifying || code.length !== 6}
                  className="w-full bg-blue-600 text-white font-medium py-2.5 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isVerifying ? (
                    <div className="flex items-center justify-center space-x-2">
                      <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>Verifying...</span>
                    </div>
                  ) : (
                    'Verify Code'
                  )}
                </button>

                {email && (
                  <button
                    onClick={handleShowQR}
                    className="w-full bg-white text-gray-700 font-medium py-2.5 px-4 border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                  >
                    Show QR Code Again
                  </button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TwoFAModal;
