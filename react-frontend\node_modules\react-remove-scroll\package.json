{"name": "react-remove-scroll", "version": "2.7.1", "description": "Disables scroll outside of `children` node.", "main": "dist/es5/index.js", "jsnext:main": "dist/es2015/index.js", "module": "dist/es2015/index.js", "types": "dist/es5/index.d.ts", "module:es2019": "dist/es2019/index.js", "scripts": {"dev": "lib-builder dev", "test": "jest", "test:ci": "jest --runInBand --coverage", "build": "lib-builder build && yarn size:report", "release": "yarn build && yarn test", "size": "size-limit", "size:report": "size-limit --json > .size.json", "lint": "lib-builder lint", "format": "lib-builder format", "update": "lib-builder update", "prepublish": "yarn build && yarn changelog", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "changelog:rewrite": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0", "storybook": "start-storybook -p 6006"}, "keywords": ["scroll"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@types/react": "^18.0.0", "@babel/core": "^7.17.9", "@size-limit/preset-small-lib": "^11.0.2", "size-limit": "^11.0.2", "@storybook/react": "^6.4.22", "@theuiteam/lib-builder": "^0.1.4", "@types/enzyme-adapter-react-16": "^1.0.6", "babel-loader": "^8.2.4", "enzyme-adapter-react-16": "^1.15.6"}, "resolutions": {"@types/react": "^18.0.0", "tslib": "^2.1.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"@types/react": "*", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "files": ["dist", "UI", "sidecar"], "repository": "https://github.com/theKashey/react-remove-scroll", "dependencies": {"react-remove-scroll-bar": "^2.3.7", "react-style-singleton": "^2.2.3", "tslib": "^2.1.0", "use-callback-ref": "^1.3.3", "use-sidecar": "^1.1.3"}, "sideEffects": ["**/sidecar.js"], "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{ts,tsx}": ["prettier --write", "eslint --fix", "git add"], "*.{js,css,json,md}": ["prettier --write", "git add"]}, "prettier": {"printWidth": 120, "trailingComma": "es5", "tabWidth": 2, "semi": true, "singleQuote": true}, "packageManager": "yarn@1.22.19"}