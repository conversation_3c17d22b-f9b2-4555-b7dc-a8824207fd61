import { useState, useEffect } from 'react';
import { User } from '@/types';
import { authAPI } from '@/utils/api';

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for existing session on mount
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      setIsLoading(true);
      // Optionally, call backend to check session
      const savedUser = localStorage.getItem('user');
      if (savedUser) {
        const userData = JSON.parse(savedUser);
        setUser(userData);
        document.body.classList.remove('not-logged-in');
        document.body.classList.add('logged-in');
        document.documentElement.classList.remove('not-logged-in');
        document.documentElement.classList.add('logged-in');
      } else {
        document.body.classList.add('not-logged-in');
        document.body.classList.remove('logged-in');
        document.documentElement.classList.add('not-logged-in');
        document.documentElement.classList.remove('logged-in');
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      const result = await authAPI.login(email, password);
      if (result.success) {
        if (result.require2FA) {
          // 2FA required, do not set user yet
          return { success: false, require2FA: true };
        }
        const userData: User = result.user;
        setUser(userData);
        localStorage.setItem('user', JSON.stringify(userData));
        document.body.classList.remove('not-logged-in');
        document.body.classList.add('logged-in');
        document.documentElement.classList.remove('not-logged-in');
        document.documentElement.classList.add('logged-in');
        return { success: true };
      } else {
        return { success: false, error: result.error || 'Login failed' };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Login failed' };
    }
  };

  const register = async (fullName: string, email: string, password: string, employeeId?: string) => {
    try {
      const result = await authAPI.register(fullName, email, password, employeeId);
      if (result.success) {
        const userData: User = result.user;
        setUser(userData);
        localStorage.setItem('user', JSON.stringify(userData));
        document.body.classList.remove('not-logged-in');
        document.body.classList.add('logged-in');
        document.documentElement.classList.remove('not-logged-in');
        document.documentElement.classList.add('logged-in');
        return { success: true };
      } else {
        return { success: false, error: result.error || 'Registration failed' };
      }
    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, error: 'Registration failed' };
    }
  };

  const logout = async () => {
    try {
      await authAPI.logout();
    } catch (e) {
      // Ignore errors on logout
    }
    setUser(null);
    localStorage.removeItem('user');
    document.body.classList.add('not-logged-in');
    document.body.classList.remove('logged-in');
    document.documentElement.classList.add('not-logged-in');
    document.documentElement.classList.remove('logged-in');
  };

  const verify2FA = async (code: string) => {
    try {
      const result = await authAPI.verify2FA(code);
      if (result.success) {
        const userData: User = result.user;
        setUser(userData);
        localStorage.setItem('user', JSON.stringify(userData));
        document.body.classList.remove('not-logged-in');
        document.body.classList.add('logged-in');
        document.documentElement.classList.remove('not-logged-in');
        document.documentElement.classList.add('logged-in');
        return { success: true };
      } else {
        return { success: false, error: result.error || '2FA verification failed' };
      }
    } catch (error) {
      console.error('2FA verification error:', error);
      return { success: false, error: '2FA verification failed' };
    }
  };

  const updateUser = (updates: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...updates };
      setUser(updatedUser);
      localStorage.setItem('user', JSON.stringify(updatedUser));
    }
  };

  return {
    user,
    isLoading,
    isLoggedIn: !!user,
    login,
    register,
    logout,
    updateUser,
    checkAuthStatus,
    verify2FA,
  };
};
