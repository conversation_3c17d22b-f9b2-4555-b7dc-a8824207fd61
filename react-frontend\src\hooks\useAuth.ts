import { useState, useEffect } from 'react';
import { User } from '@/types';
import { authAPI } from '@/utils/api';

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [pendingLogin, setPendingLogin] = useState<{email: string, password: string} | null>(null);

  useEffect(() => {
    // Check for existing session on mount
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      setIsLoading(true);
      // Optionally, call backend to check session
      const savedUser = localStorage.getItem('user');
      if (savedUser) {
        const userData = JSON.parse(savedUser);
        setUser(userData);
        document.body.classList.remove('not-logged-in');
        document.body.classList.add('logged-in');
        document.documentElement.classList.remove('not-logged-in');
        document.documentElement.classList.add('logged-in');
      } else {
        document.body.classList.add('not-logged-in');
        document.body.classList.remove('logged-in');
        document.documentElement.classList.add('not-logged-in');
        document.documentElement.classList.remove('logged-in');
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      const result = await authAPI.login(email, password);
      if (result.success) {
        const userData: User = result.user;
        setUser(userData);
        localStorage.setItem('user', JSON.stringify(userData));
        localStorage.setItem('token', result.token);
        setPendingLogin(null);
        document.body.classList.remove('not-logged-in');
        document.body.classList.add('logged-in');
        document.documentElement.classList.remove('not-logged-in');
        document.documentElement.classList.add('logged-in');
        return { success: true };
      } else {
        // Check if 2FA is required
        if (result.message === "2FA code required") {
          setPendingLogin({ email, password });
          return { success: false, require2FA: true };
        }
        return { success: false, error: result.message || 'Login failed' };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Network error. Please try again.' };
    }
  };

  const register = async (fullName: string, email: string, password: string, employeeId?: string) => {
    try {
      const result = await authAPI.register(fullName, email, password, employeeId);
      if (result.success) {
        // Registration successful, return QR codes for 2FA setup
        return {
          success: true,
          qrCodeUser: result['2fa_qr_url_user'],
          qrCodeAdmin: result['2fa_qr_url_admin'],
          message: result.message
        };
      } else {
        return { success: false, error: result.message || 'Registration failed' };
      }
    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, error: 'Network error. Please try again.' };
    }
  };

  const logout = async () => {
    try {
      await authAPI.logout();
    } catch (e) {
      // Ignore errors on logout
    }
    setUser(null);
    setPendingLogin(null);
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    document.body.classList.add('not-logged-in');
    document.body.classList.remove('logged-in');
    document.documentElement.classList.add('not-logged-in');
    document.documentElement.classList.remove('logged-in');
  };

  const verify2FA = async (code: string) => {
    try {
      if (!pendingLogin) {
        return { success: false, error: 'No pending login found' };
      }

      const result = await authAPI.login(pendingLogin.email, pendingLogin.password, code);
      if (result.success) {
        const userData: User = result.user;
        setUser(userData);
        localStorage.setItem('user', JSON.stringify(userData));
        localStorage.setItem('token', result.token);
        setPendingLogin(null);
        document.body.classList.remove('not-logged-in');
        document.body.classList.add('logged-in');
        document.documentElement.classList.remove('not-logged-in');
        document.documentElement.classList.add('logged-in');
        return { success: true };
      } else {
        return { success: false, error: result.message || '2FA verification failed' };
      }
    } catch (error) {
      console.error('2FA verification error:', error);
      return { success: false, error: 'Network error. Please try again.' };
    }
  };

  const get2FASetup = async (email: string) => {
    try {
      const result = await authAPI.get2FASetup(email);
      return result;
    } catch (error) {
      console.error('2FA setup error:', error);
      return { success: false, error: 'Failed to get 2FA setup' };
    }
  };

  const updateUser = (updates: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...updates };
      setUser(updatedUser);
      localStorage.setItem('user', JSON.stringify(updatedUser));
    }
  };

  return {
    user,
    isLoading,
    isLoggedIn: !!user,
    login,
    register,
    logout,
    updateUser,
    checkAuthStatus,
    verify2FA,
    get2FASetup,
    pendingLogin,
  };
};
