import React, { useState } from 'react';

interface RegisterModalProps {
  onClose: () => void;
  onRegister: (fullName: string, email: string, password: string, employeeId?: string) => Promise<{ success: boolean; error?: string }>;
  onSwitchToLogin: () => void;
}

const RegisterModal: React.FC<RegisterModalProps> = ({
  onClose,
  onRegister,
  onSwitchToLogin,
}) => {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    password: '',
    employeeId: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    setError(''); // Clear error when user types
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.fullName || !formData.email || !formData.password) {
      setError('Please fill in all required fields');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const result = await onRegister(
        formData.fullName,
        formData.email,
        formData.password,
        formData.employeeId || undefined
      );
      if (result.success) {
        onClose();
      } else {
        setError(result.error || 'Registration failed');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="modal-overlay flex items-center justify-center fixed inset-0 z-50 bg-black/40 backdrop-blur-sm px-2 sm:px-0" onClick={handleOverlayClick}>
      <div className="modal-content bg-white rounded-3xl shadow-2xl w-full max-w-md mx-auto p-4 sm:p-6 relative flex flex-col gap-5 min-w-0" style={{ minWidth: undefined }}>
        {/* Header */}
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-bold text-black">Register</h3>
          <button
            onClick={onClose}
            className="icon-btn text-black hover:bg-neutral-100 rounded-full p-1.5 transition-colors text-base"
          >
            <i className="fas fa-times"></i>
          </button>
        </div>
        {/* Body */}
        <form onSubmit={handleSubmit} className="flex flex-col gap-4">
          {/* Full Name */}
          <div className="flex flex-col gap-1">
            <label htmlFor="fullName" className="text-sm font-medium text-black">Full Name <span className="text-red-500">*</span></label>
            <input
              type="text"
              id="fullName"
              name="fullName"
              value={formData.fullName}
              onChange={handleInputChange}
              className="rounded-xl border border-neutral-300 focus:ring-2 focus:ring-[#007aff] focus:border-[#007aff] px-3 py-2 text-base bg-white text-black placeholder-neutral-400 transition-all outline-none"
              placeholder="Enter your full name"
              autoComplete="name"
              disabled={isLoading}
            />
          </div>
          {/* Email */}
          <div className="flex flex-col gap-1">
            <label htmlFor="email" className="text-sm font-medium text-black">Email Address <span className="text-red-500">*</span></label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="rounded-xl border border-neutral-300 focus:ring-2 focus:ring-[#007aff] focus:border-[#007aff] px-3 py-2 text-base bg-white text-black placeholder-neutral-400 transition-all outline-none"
              placeholder="Enter your email"
              autoComplete="email"
              disabled={isLoading}
            />
          </div>
          {/* Password */}
          <div className="flex flex-col gap-1">
            <label htmlFor="password" className="text-sm font-medium text-black">Password <span className="text-red-500">*</span></label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                className="rounded-xl border border-neutral-300 focus:ring-2 focus:ring-[#007aff] focus:border-[#007aff] px-3 py-2 text-base bg-white text-black placeholder-neutral-400 pr-10 transition-all outline-none w-full"
                placeholder="Create a password"
                autoComplete="new-password"
                disabled={isLoading}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-500 hover:text-[#007aff] focus:outline-none text-base"
                tabIndex={-1}
              >
                <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
              </button>
            </div>
          </div>
          {/* Employee ID */}
          <div className="flex flex-col gap-1">
            <label htmlFor="employeeId" className="text-sm font-medium text-black">Employee ID</label>
            <input
              type="text"
              id="employeeId"
              name="employeeId"
              value={formData.employeeId}
              onChange={handleInputChange}
              className="rounded-xl border border-neutral-300 focus:ring-2 focus:ring-[#007aff] focus:border-[#007aff] px-3 py-2 text-base bg-white text-black placeholder-neutral-400 transition-all outline-none"
              placeholder="Enter your employee ID (Optional)"
              autoComplete="off"
              disabled={isLoading}
            />
          </div>
          {/* Error Message */}
          {error && (
            <div className="p-2 bg-red-50 border border-red-200 rounded-md text-sm text-red-600">
              {error}
            </div>
          )}
          {/* Submit Button */}
          <button
            type="submit"
            className="w-full rounded-full bg-[#007aff] text-white font-semibold py-2.5 text-base shadow hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-[#007aff] disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="loading-spinner"></div>
                <span>Creating Account...</span>
              </div>
            ) : (
              'Create Account'
            )}
          </button>
          {/* Divider */}
          <div className="flex items-center gap-2 my-1">
            <div className="flex-1 h-px bg-neutral-200" />
            <span className="text-sm text-neutral-400">or</span>
            <div className="flex-1 h-px bg-neutral-200" />
          </div>
          {/* Switch to Login */}
          <button
            type="button"
            onClick={onSwitchToLogin}
            className="w-full rounded-full border border-[#007aff] bg-white text-[#007aff] font-semibold py-2.5 text-base shadow hover:bg-blue-50 transition-colors focus:outline-none focus:ring-2 focus:ring-[#007aff] disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isLoading}
          >
            Sign In Instead
          </button>
        </form>
      </div>
    </div>
  );
};

export default RegisterModal;
