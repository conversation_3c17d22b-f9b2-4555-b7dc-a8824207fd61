import React, { useState } from 'react';
import { useTheme } from '@/hooks/useTheme';
import { useAuth } from '@/hooks/useAuth';
import { useChat } from '@/hooks/useChat';
import { ModalState } from '@/types';

// Layout components
import Header from '@/components/layout/Header';
import Sidebar from '@/components/layout/Sidebar';
import ChatContainer from '@/components/chat/ChatContainer';
import PreLoginUI from '@/components/auth/PreLoginUI';

// Modal components
import LoginModal from '@/components/modals/LoginModal';
import RegisterModal from '@/components/modals/RegisterModal';
import SettingsModal from '@/components/modals/SettingsModal';
import VoiceModal from '@/components/modals/VoiceModal';
import FilePreviewModal from '@/components/modals/FilePreviewModal';
import EscalationModal from '@/components/modals/EscalationModal';
import ArchivedChatsModal from '@/components/modals/ArchivedChatsModal';
import TwoFAModal from '@/components/modals/TwoFAModal';

function App() {
  const { theme, changeTheme, isDark } = useTheme();
  const { user, isLoading, isLoggedIn, login, register, logout, updateUser, verify2FA, pendingLogin } = useAuth();
  const chatHook = useChat();

  const [modals, setModals] = useState<ModalState>({
    login: false,
    register: false,
    settings: false,
    voice: false,
    filePreview: false,
    escalation: false,
    archivedChats: false,
    twoFA: false,
  });

  const [sidebarCollapsed, setSidebarCollapsed] = useState(() => {
    return localStorage.getItem('sidebarCollapsed') === 'true';
  });

  const openModal = (modalName: keyof ModalState) => {
    setModals(prev => ({ ...prev, [modalName]: true }));
  };

  const closeModal = (modalName: keyof ModalState) => {
    setModals(prev => ({ ...prev, [modalName]: false }));
  };

  const closeAllModals = () => {
    setModals({
      login: false,
      register: false,
      settings: false,
      voice: false,
      filePreview: false,
      escalation: false,
      archivedChats: false,
      twoFA: false,
    });
  };

  const toggleSidebar = () => {
    const newCollapsed = !sidebarCollapsed;
    setSidebarCollapsed(newCollapsed);
    localStorage.setItem('sidebarCollapsed', newCollapsed.toString());
  };

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="loading-spinner"></div>
      </div>
    );
  }

  return (
    <div className="h-screen overflow-hidden">
      {!isLoggedIn ? (
        <PreLoginUI
          onLogin={() => openModal('login')}
          onRegister={() => openModal('register')}
          onSendMessage={chatHook.sendMessage}
        />
      ) : (
        <div className="app-container flex h-full">
          {/* Sidebar */}
          <Sidebar
            isCollapsed={sidebarCollapsed}
            onToggle={toggleSidebar}
            chatSessions={chatHook.chatSessions}
            currentSessionId={chatHook.currentSessionId}
            onNewChat={chatHook.createNewSession}
            onLoadSession={chatHook.loadSession}
            onDeleteSession={chatHook.deleteSession}
            onArchiveSession={chatHook.archiveSession}
            onOpenSearch={() => {/* TODO: Implement search */}}
            onOpenArchivedChats={() => openModal('archivedChats')}
          />

          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            {/* Header */}
            <Header
              user={user}
              sidebarCollapsed={sidebarCollapsed}
              onToggleSidebar={toggleSidebar}
              onNewChat={chatHook.createNewSession}
              onOpenSettings={() => openModal('settings')}
              onLogout={logout}
            />

            {/* Chat Container */}
            <ChatContainer
              messages={chatHook.messages}
              isLoading={chatHook.isLoading}
              attachedFiles={chatHook.attachedFiles}
              onSendMessage={chatHook.sendMessage}
              onAddFile={chatHook.addFileAttachment}
              onRemoveFile={chatHook.removeFileAttachment}
              onOpenVoice={() => openModal('voice')}
              onOpenEscalation={() => openModal('escalation')}
              user={user}
            />
          </div>
        </div>
      )}

      {/* Modals */}
      {modals.login && (
          <LoginModal
            onClose={() => closeModal('login')}
            onLogin={login}
            onSwitchToRegister={() => {
              closeModal('login');
              openModal('register');
            }}
            onOpen2FA={() => {
              closeModal('login');
              openModal('twoFA');
            }}
          />
      )}

      {modals.register && (
          <RegisterModal
            onClose={() => closeModal('register')}
            onRegister={register}
            onSwitchToLogin={() => {
              closeModal('register');
              openModal('login');
            }}
          />
      )}

      {modals.settings && (
          <SettingsModal
            onClose={() => closeModal('settings')}
            theme={theme}
            onThemeChange={changeTheme}
            user={user}
            onUpdateUser={updateUser}
            onLogout={logout}
            onClearAllChats={chatHook.clearAllChats}
            onOpenArchivedChats={() => {
              closeModal('settings');
              openModal('archivedChats');
            }}
          />
      )}

      {modals.voice && (
          <VoiceModal
            onClose={() => closeModal('voice')}
            onSubmitTranscript={(transcript) => {
              chatHook.sendMessage(transcript);
              closeModal('voice');
            }}
          />
      )}

      {modals.escalation && (
          <EscalationModal
            onClose={() => closeModal('escalation')}
            user={user}
            onSubmit={(formData) => {
              // TODO: Handle escalation submission
              console.log('Escalation submitted:', formData);
              closeModal('escalation');
            }}
          />
      )}

      {modals.archivedChats && (
          <ArchivedChatsModal
            onClose={() => closeModal('archivedChats')}
            archivedSessions={chatHook.archivedSessions}
            onRestoreSession={(sessionId) => {
              // TODO: Implement restore functionality
              console.log('Restore session:', sessionId);
            }}
          />
      )}

      {modals.twoFA && (
          <TwoFAModal
            onClose={() => closeModal('twoFA')}
            email={pendingLogin?.email}
            onVerify={async (code) => {
              const result = await verify2FA(code);
              if (result.success) {
                closeModal('twoFA');
              }
              return result;
            }}
          />
        )}
    </div>
  );
}

export default App;
