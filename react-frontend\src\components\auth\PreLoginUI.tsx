import React, { useState } from 'react';
import SuggestionChips from '@/components/chat/SuggestionChips';

interface PreLoginUIProps {
  onLogin: () => void;
  onRegister: () => void;
  onSendMessage: (message: string) => void;
}

const PreLoginUI: React.FC<PreLoginUIProps> = ({ onLogin, onRegister, onSendMessage }) => {
  const [inputValue, setInputValue] = useState('');
  const [showSendButton, setShowSendButton] = useState(false);

  const suggestions = [
    { text: "🗓️ Leave Policy", query: "What is the company's leave policy?" },
    { text: "👥 Referral Program", query: "How does the employee referral program work?" },
    { text: "👔 Dress Code", query: "What is the dress code policy?" },
    { text: "🏠 Work from Home", query: "Tell me about the work from home policy" },
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setInputValue(value);
    setShowSendButton(value.trim().length > 0);
    
    // Auto-resize textarea
    e.target.style.height = 'auto';
    e.target.style.height = e.target.scrollHeight + 'px';
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim()) {
      onSendMessage(inputValue.trim());
      setInputValue('');
      setShowSendButton(false);
    }
  };

  const handleSuggestionClick = (query: string) => {
    onSendMessage(query);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <div className="pre-login-container" role="main" aria-label="Pre-login chat interface">
      {/* Header */}
      <header className="pre-login-header" role="banner">
        <div className="pre-login-logo" aria-label="ZiaHR logo and brand">
          <i className="fas fa-comment-dots text-black" aria-hidden="true"></i>
          <span className="font-bold text-xl text-black">ZiaHR</span>
        </div>
        <div className="flex gap-2 pre-login-actions">
          <button
            className="login-btn rounded-full px-5 py-2 font-semibold bg-black text-white shadow transition-colors hover:bg-neutral-800 focus:outline-none focus:ring-2 focus:ring-black"
            id="preLoginBtn"
            aria-label="Log in to your account"
            onClick={onLogin}
            type="button"
          >
            Log in
          </button>
          <button
            className="signup-btn rounded-full px-5 py-2 font-semibold bg-white text-black border border-black shadow transition-colors hover:bg-neutral-100 focus:outline-none focus:ring-2 focus:ring-black"
            id="preSignupBtn"
            aria-label="Sign up for a new account"
            onClick={onRegister}
            type="button"
          >
            Sign up
          </button>
        </div>
      </header>

      {/* Main Content */}
      <main className="pre-login-main flex flex-col items-center justify-center min-h-[calc(100vh-80px)] bg-white" aria-labelledby="prelogin-welcome-heading">
        {/* Greeting message box */}
        <div className="greeting-message-box bg-white rounded-3xl shadow-xl px-6 py-5 mb-6 flex flex-col items-center max-w-xl w-full border border-neutral-100" style={{ zIndex: 2 }}>
          <h2 id="prelogin-welcome-heading" className="text-lg font-bold text-black mb-2 tracking-tight" style={{ fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif' }}>👋 Welcome to ZiaHR</h2>
          <p className="text-base text-neutral-700 mb-3 text-center" style={{ fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif' }}>
            I can help you with questions about company policies, employee guidelines, and HR procedures.
          </p>
          <div className="suggestion-chips flex flex-row flex-wrap justify-center items-center gap-2 mt-1">
            {suggestions.map((suggestion, index) => (
              <button
                key={index}
                className="suggestion-chip rounded-full px-3 py-1.5 bg-neutral-100 text-black border border-neutral-200 shadow-sm hover:bg-neutral-200 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm font-medium"
                onClick={() => handleSuggestionClick(suggestion.query)}
                type="button"
                style={{ fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif' }}
              >
                {suggestion.text}
              </button>
            ))}
          </div>
        </div>
        {/* Input container - Apple style, floating below greeting */}
        <div className="w-full flex flex-col items-center" style={{ marginTop: '-20px', zIndex: 1 }}>
          <form className="pre-login-chat-input-form w-full max-w-xl" onSubmit={handleSubmit}>
            <div className="flex items-center bg-white rounded-full shadow-lg px-4 py-2 border border-neutral-100 focus-within:ring-2 focus-within:ring-blue-500 transition-all" style={{ minHeight: 40 }}>
              <textarea
                id="preLoginChatInput"
                className="pre-login-input flex-1 bg-transparent border-none outline-none text-base text-black placeholder-neutral-400 resize-none font-medium"
                placeholder="How can I help you today?"
                rows={1}
                aria-label="Type your question here"
                aria-multiline="true"
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                style={{ boxShadow: 'none', fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif' }}
              />
              <div className="flex items-center gap-2 ml-2">
                <button type="button" className="chatgpt-tool-btn pre-login-action-btn rounded-full bg-neutral-100 text-black p-2 shadow-sm hover:bg-neutral-200 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 text-base" aria-label="Attach a file">
                  <i className="fas fa-paperclip"></i>
                </button>
                <button type="button" className="chatgpt-tool-btn pre-login-action-btn rounded-full bg-neutral-100 text-black p-2 shadow-sm hover:bg-neutral-200 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 text-base" aria-label="Start voice input">
                  <i className="fas fa-microphone"></i>
                </button>
                {showSendButton && (
                  <button
                    type="submit"
                    className="chatgpt-send-btn pre-login-send-btn rounded-full bg-[#007aff] text-white px-4 py-2 font-semibold shadow transition-colors hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                    title="Send message"
                    aria-label="Send message"
                    style={{ minWidth: 36 }}
                  >
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M7 11L12 6L17 11M12 18V7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </button>
                )}
              </div>
            </div>
          </form>
        </div>
      </main>
    </div>
  );
};

export default PreLoginUI;
